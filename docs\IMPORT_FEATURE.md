# CSV Import Feature Documentation

## Overview

The CSV Import Feature allows you to import report data from CSV files stored in the `storage/app/private/seed` folder. The system automatically processes CSV files with the naming convention `{report_code}_{month}{year}.csv` and imports the data into the appropriate database tables.

## Features

### 1. **Automatic CSV Processing**
- **Separator Detection**: Automatically detects CSV separators (comma, semicolon, tab, pipe)
- **BOM Handling**: Removes Byte Order Mark (BOM) characters from CSV files
- **Decimal Formatting**: Converts comma decimal separators to dots for database compatibility
- **Header Validation**: Validates CSV structure against expected report schemas

### 2. **Report Batch Management**
- **Automatic Batch Creation**: Creates report batches for new periods automatically
- **Period Detection**: Extracts month and year from filename
- **Status Tracking**: Tracks batch processing status (processing → completed)

### 3. **Data Import Logic**
- **Replace Strategy**: Deletes existing data before importing new data
- **Transaction Safety**: Uses database transactions to ensure data integrity
- **Batch Processing**: Processes large files in chunks for better performance
- **Error Handling**: Comprehensive error handling with rollback capabilities

### 4. **Notification System**
- **Import Tracking**: Stores detailed information about each import
- **Success/Failure Status**: Tracks import status with error messages
- **Performance Metrics**: Records processing time and record counts
- **History**: Maintains complete import history with filtering capabilities

## File Naming Convention

CSV files must follow this naming pattern:
```
{report_code}_{month}{year}.csv
```

**Examples:**
- `A01_042025.csv` - A01 report for April 2025
- `D01_122024.csv` - D01 report for December 2024
- `P01_062025.csv` - P01 report for June 2025

## Supported Report Types

The system currently supports the following report types:

| Report Code | Description | Table Name |
|-------------|-------------|------------|
| A01 | Agunan Report | report_a01 |
| D01 | Debitur Perorangan | report_d01 |
| D02 | Debitur Badan Usaha | report_d02 |
| F01 | Fasilitas Report | report_f01 |
| F05 | Fasilitas Lainnya | report_f05 |
| P01 | Penjamin Report | report_p01 |

## Usage

### Web Interface

1. **Access Import Page**: Navigate to `/import` in your browser
2. **View Available Files**: See all CSV files in the seed directory
3. **Preview Files**: Click "Preview" to see CSV structure and first few rows
4. **Import Files**: Click "Import" to process the CSV file
5. **View History**: See recent imports with status and record counts

### API Endpoints

#### Get Available Files
```http
GET /import/files
```

#### Preview CSV File
```http
POST /import/preview
Content-Type: application/json

{
    "filename": "A01_042025.csv"
}
```

#### Import CSV File
```http
POST /import/file
Content-Type: application/json

{
    "filename": "A01_042025.csv"
}
```

#### Get Import History
```http
GET /import/history?report_code=A01&status=success&year=2025&month=4
```

### Command Line Usage

You can also import files programmatically using the service:

```php
use App\Services\ReportImportService;

$importService = app(ReportImportService::class);
$notification = $importService->importFromCsv('seed/A01_042025.csv');

if ($notification->status === 'success') {
    echo "Imported {$notification->records_imported} records";
} else {
    echo "Import failed: {$notification->error_message}";
}
```

## CSV Format Requirements

### General Requirements
- **Encoding**: UTF-8 (with or without BOM)
- **Separators**: Comma (,), Semicolon (;), Tab (\t), or Pipe (|)
- **Headers**: First row must contain column headers
- **Data Types**: Text, numbers, dates in YYYYMMDD format

### Decimal Values
- **Input Format**: Can use comma (,) or dot (.) as decimal separator
- **Examples**: `19,6` or `19.6` both accepted
- **Output**: Automatically converted to dot notation for database storage

### Date Values
- **Format**: YYYYMMDD (e.g., 20250415 for April 15, 2025)
- **No Separators**: Dates should not contain dashes or slashes

## Error Handling

### Common Errors and Solutions

1. **Invalid Filename Format**
   - **Error**: "Invalid filename format"
   - **Solution**: Ensure filename follows `{report_code}_{month}{year}.csv` pattern

2. **File Not Found**
   - **Error**: "File not found"
   - **Solution**: Place CSV file in `storage/app/private/seed` directory

3. **CSV Parsing Errors**
   - **Error**: "Could not detect CSV separator"
   - **Solution**: Ensure CSV uses supported separators and has consistent structure

4. **Missing Headers**
   - **Error**: "Missing required headers"
   - **Solution**: Verify CSV headers match expected report schema

5. **Database Errors**
   - **Error**: SQL constraint violations
   - **Solution**: Check data types and formats, especially decimal values

## Database Schema

### Import Notifications Table
```sql
CREATE TABLE import_notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    report_code VARCHAR(10) NOT NULL,
    report_month TINYINT UNSIGNED NOT NULL,
    report_year SMALLINT UNSIGNED NOT NULL,
    records_imported INT DEFAULT 0,
    status ENUM('success', 'failed') DEFAULT 'success',
    error_message LONGTEXT NULL,
    import_details JSON NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_report_period (report_code, report_year, report_month),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

## Performance Considerations

### Large File Handling
- **Batch Processing**: Files are processed in chunks of 500 records
- **Memory Management**: Uses streaming for large files
- **Transaction Optimization**: Minimizes transaction overhead

### Recommended Limits
- **File Size**: Up to 100MB per CSV file
- **Record Count**: Up to 1 million records per file
- **Concurrent Imports**: Avoid importing multiple files simultaneously

## Security

### Access Control
- **Authentication Required**: All import endpoints require user authentication
- **File Path Validation**: Only files in the seed directory can be imported
- **Filename Validation**: Strict validation of filename patterns

### Data Validation
- **Input Sanitization**: All CSV data is cleaned and validated
- **SQL Injection Prevention**: Uses parameterized queries
- **Transaction Safety**: Rollback on any error during import

## Monitoring and Logging

### Import Notifications
- All imports are logged in the `import_notifications` table
- Success/failure status with detailed error messages
- Performance metrics (processing time, record counts)

### Application Logs
- Detailed error logs in `storage/logs/laravel.log`
- Import progress tracking
- Performance monitoring

## Troubleshooting

### Debug Mode
Enable debug mode to see detailed error information:
```bash
php artisan config:set app.debug=true
```

### Check Logs
View recent import logs:
```bash
tail -f storage/logs/laravel.log | grep "Report import"
```

### Test Import
Test import functionality with sample data:
```bash
php artisan test tests/Feature/ImportTest.php
```

## Future Enhancements

### Planned Features
- **Bulk Import**: Import multiple files at once
- **Scheduled Imports**: Automatic import on file upload
- **Data Validation Rules**: Configurable validation rules per report type
- **Import Templates**: Generate CSV templates for each report type
- **Progress Tracking**: Real-time import progress for large files
